/* Silksong Wiki - Main Styles */

/* CSS Variables - Silksong Color Palette */
:root {
    /* Primary Colors - Based on Silksong's aesthetic */
    --primary-red: #d63031;
    --primary-dark-red: #a71e1f;
    --secondary-gold: #f39c12;
    --accent-purple: #6c5ce7;
    --accent-pink: #fd79a8;
    
    /* Background Colors */
    --bg-primary: #0d1117;
    --bg-secondary: #161b22;
    --bg-tertiary: #21262d;
    --bg-card: #1c2128;
    --bg-overlay: rgba(13, 17, 23, 0.8);
    
    /* Text Colors */
    --text-primary: #f0f6fc;
    --text-secondary: #8b949e;
    --text-muted: #6e7681;
    --text-accent: #ffa8a8;
    
    /* Border Colors */
    --border-primary: #30363d;
    --border-secondary: #21262d;
    --border-accent: #d63031;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #d63031 0%, #a71e1f 100%);
    --gradient-secondary: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    --gradient-hero: linear-gradient(135deg, rgba(214, 48, 49, 0.9) 0%, rgba(167, 30, 31, 0.9) 100%);
    
    /* Shadows */
    --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.4);
    --shadow-large: 0 8px 16px rgba(0, 0, 0, 0.5);
    --shadow-glow: 0 0 20px rgba(214, 48, 49, 0.3);
    
    /* Typography */
    --font-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-heading: 'Georgia', 'Times New Roman', serif;
    --font-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-red);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--text-accent);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Header Styles */
.site-header {
    background-color: var(--bg-secondary);
    border-bottom: 2px solid var(--border-accent);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow-medium);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    max-width: 1200px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.logo {
    height: 40px;
    width: auto;
    margin-right: var(--spacing-sm);
}

.logo-text {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-primary);
    text-shadow: 0 0 10px var(--primary-red);
}

/* Navigation */
.main-navigation {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
    margin: 0 var(--spacing-sm);
}

.nav-link {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-weight: 500;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.nav-link:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-accent);
    box-shadow: var(--shadow-small);
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-large);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    z-index: 1001;
}

.nav-item:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    list-style: none;
}

.dropdown-menu a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-secondary);
    transition: all var(--transition-fast);
}

.dropdown-menu a:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.dropdown-menu li:last-child a {
    border-bottom: none;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 0.9rem;
    width: 250px;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-red);
    box-shadow: 0 0 0 2px rgba(214, 48, 49, 0.2);
}

.search-button {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    margin-left: -40px;
    z-index: 1;
    transition: color var(--transition-fast);
}

.search-button:hover {
    color: var(--primary-red);
}

.search-icon {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: all var(--transition-fast);
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    border: none;
    transition: all var(--transition-fast);
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-small);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-accent);
}

.btn-secondary:hover {
    background-color: var(--primary-red);
    color: white;
    transform: translateY(-2px);
}

/* Section Styles */
.section-title {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
    color: var(--text-primary);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-container {
        flex-wrap: wrap;
    }
    
    .main-navigation {
        display: none;
        width: 100%;
        order: 3;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .search-input {
        width: 200px;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.25rem; }
    
    .container {
        padding: 0 var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .search-input {
        width: 150px;
    }
    
    .logo-text {
        font-size: 1.25rem;
    }
    
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
}
