/* Silksong Wiki - Component Styles */

/* Hero Section */
.hero-section {
    position: relative;
    height: 70vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.4) contrast(1.2);
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hero);
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    max-width: 800px;
    padding: var(--spacing-xl);
}

.hero-title {
    font-size: 4rem;
    font-weight: bold;
    margin-bottom: var(--spacing-md);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    background: linear-gradient(45deg, #fff, #ffa8a8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--secondary-gold);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.hero-description {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xl);
    color: var(--text-primary);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

/* Quick Navigation Section */
.quick-nav-section {
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-secondary);
}

.quick-nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.nav-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(214, 48, 49, 0.1), transparent);
    transition: left var(--transition-slow);
}

.nav-card:hover::before {
    left: 100%;
}

.nav-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
    border-color: var(--primary-red);
}

.card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-glow);
}

.card-icon img {
    width: 50px;
    height: 50px;
    filter: brightness(0) invert(1);
}

.card-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1.25rem;
}

.card-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.card-link {
    display: inline-block;
    color: var(--primary-red);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-fast);
    position: relative;
}

.card-link::after {
    content: '→';
    margin-left: var(--spacing-sm);
    transition: transform var(--transition-fast);
}

.card-link:hover::after {
    transform: translateX(5px);
}

/* Featured Section */
.featured-section {
    padding: var(--spacing-xxl) 0;
    background-color: var(--bg-primary);
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.featured-article {
    background-color: var(--bg-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 1px solid var(--border-primary);
}

.featured-article:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
    border-color: var(--primary-red);
}

.article-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.featured-article:hover .article-image img {
    transform: scale(1.05);
}

.article-content {
    padding: var(--spacing-xl);
}

.article-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    font-size: 1.25rem;
}

.article-excerpt {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.article-link {
    color: var(--primary-red);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all var(--transition-fast);
}

.article-link:hover {
    color: var(--text-accent);
}

/* Footer */
.site-footer {
    background-color: var(--bg-secondary);
    border-top: 2px solid var(--border-accent);
    padding: var(--spacing-xxl) 0 var(--spacing-xl);
    margin-top: var(--spacing-xxl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section {
    color: var(--text-secondary);
}

.footer-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    font-size: 1.125rem;
    position: relative;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--gradient-primary);
}

.footer-description {
    line-height: 1.6;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: var(--text-secondary);
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-red);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--border-primary);
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .quick-nav-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .featured-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .nav-card,
    .featured-article {
        margin: 0 var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .hero-section {
        height: 60vh;
        min-height: 400px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-content {
        padding: var(--spacing-lg);
    }
    
    .quick-nav-section,
    .featured-section {
        padding: var(--spacing-xl) 0;
    }
    
    .card-icon {
        width: 60px;
        height: 60px;
    }
    
    .card-icon img {
        width: 35px;
        height: 35px;
    }
}
