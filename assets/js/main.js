// Silksong Wiki - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initializeNavigation();
    initializeSearch();
    initializeMobileMenu();
    initializeScrollEffects();
    initializeAnimations();
});

// Navigation functionality
function initializeNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        const dropdown = item.querySelector('.dropdown-menu');
        if (dropdown) {
            let timeout;
            
            item.addEventListener('mouseenter', () => {
                clearTimeout(timeout);
                dropdown.style.opacity = '1';
                dropdown.style.visibility = 'visible';
                dropdown.style.transform = 'translateY(0)';
            });
            
            item.addEventListener('mouseleave', () => {
                timeout = setTimeout(() => {
                    dropdown.style.opacity = '0';
                    dropdown.style.visibility = 'hidden';
                    dropdown.style.transform = 'translateY(-10px)';
                }, 150);
            });
        }
    });
}

// Search functionality
function initializeSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchButton = document.querySelector('.search-button');
    
    if (searchInput && searchButton) {
        // Sample search data - in a real implementation, this would come from a database
        const searchData = [
            { title: 'Hornet Combat Guide', url: 'hornet/needle-combat.html', category: 'Combat' },
            { title: 'Kingdom of Pharloom', url: 'areas/overview.html', category: 'Areas' },
            { title: 'Silk Abilities', url: 'hornet/silk-abilities.html', category: 'Abilities' },
            { title: 'Boss Battles', url: 'combat/bosses.html', category: 'Combat' },
            { title: 'Character Overview', url: 'hornet/overview.html', category: 'Characters' },
            { title: 'Tools and Equipment', url: 'items/tools.html', category: 'Items' },
            { title: 'Main Story Quest', url: 'quests/main-story.html', category: 'Quests' },
            { title: 'Kingdom History', url: 'lore/kingdom-history.html', category: 'Lore' }
        ];
        
        let searchResults = [];
        let currentSearchTerm = '';
        
        // Create search results container
        const searchResultsContainer = document.createElement('div');
        searchResultsContainer.className = 'search-results';
        searchResultsContainer.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-large);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1002;
            display: none;
        `;
        
        searchInput.parentElement.appendChild(searchResultsContainer);
        
        // Search input event listener
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase().trim();
            currentSearchTerm = searchTerm;
            
            if (searchTerm.length < 2) {
                hideSearchResults();
                return;
            }
            
            searchResults = searchData.filter(item => 
                item.title.toLowerCase().includes(searchTerm) ||
                item.category.toLowerCase().includes(searchTerm)
            );
            
            displaySearchResults(searchResults);
        });
        
        // Search button click
        searchButton.addEventListener('click', function() {
            performSearch();
        });
        
        // Enter key search
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.parentElement.contains(e.target)) {
                hideSearchResults();
            }
        });
        
        function displaySearchResults(results) {
            if (results.length === 0) {
                searchResultsContainer.innerHTML = `
                    <div style="padding: var(--spacing-md); color: var(--text-muted); text-align: center;">
                        No results found for "${currentSearchTerm}"
                    </div>
                `;
            } else {
                searchResultsContainer.innerHTML = results.map(result => `
                    <a href="${result.url}" class="search-result-item" style="
                        display: block;
                        padding: var(--spacing-md);
                        color: var(--text-primary);
                        text-decoration: none;
                        border-bottom: 1px solid var(--border-secondary);
                        transition: background-color var(--transition-fast);
                    " onmouseover="this.style.backgroundColor='var(--bg-tertiary)'" 
                       onmouseout="this.style.backgroundColor='transparent'">
                        <div style="font-weight: 600;">${result.title}</div>
                        <div style="font-size: 0.9rem; color: var(--text-secondary);">${result.category}</div>
                    </a>
                `).join('');
            }
            
            searchResultsContainer.style.display = 'block';
        }
        
        function hideSearchResults() {
            searchResultsContainer.style.display = 'none';
        }
        
        function performSearch() {
            if (currentSearchTerm && searchResults.length > 0) {
                // Navigate to first result
                window.location.href = searchResults[0].url;
            }
        }
    }
}

// Mobile menu functionality
function initializeMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mainNav = document.querySelector('.main-navigation');
    
    if (mobileToggle && mainNav) {
        mobileToggle.addEventListener('click', function() {
            const isOpen = mainNav.style.display === 'block';
            
            if (isOpen) {
                mainNav.style.display = 'none';
                mobileToggle.classList.remove('active');
            } else {
                mainNav.style.display = 'block';
                mobileToggle.classList.add('active');
            }
        });
        
        // Close mobile menu when clicking on a link
        const navLinks = mainNav.querySelectorAll('a');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                mainNav.style.display = 'none';
                mobileToggle.classList.remove('active');
            });
        });
        
        // Close mobile menu when resizing to desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                mainNav.style.display = '';
                mobileToggle.classList.remove('active');
            }
        });
    }
}

// Scroll effects
function initializeScrollEffects() {
    const header = document.querySelector('.site-header');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Add/remove scrolled class for header styling
        if (scrollTop > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
        
        // Hide/show header on scroll
        if (scrollTop > lastScrollTop && scrollTop > 200) {
            header.style.transform = 'translateY(-100%)';
        } else {
            header.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });
    
    // Smooth scroll for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Animation effects
function initializeAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.nav-card, .featured-article, .section-title');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
    
    // Add CSS for animation
    const style = document.createElement('style');
    style.textContent = `
        .animate-in {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }
        
        .site-header {
            transition: transform 0.3s ease;
        }
        
        .site-header.scrolled {
            background-color: rgba(22, 27, 34, 0.95);
            backdrop-filter: blur(10px);
        }
        
        .mobile-menu-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }
        
        .mobile-menu-toggle.active span:nth-child(2) {
            opacity: 0;
        }
        
        .mobile-menu-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }
        
        @media (max-width: 768px) {
            .main-navigation {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: var(--bg-secondary);
                border-top: 1px solid var(--border-primary);
                box-shadow: var(--shadow-large);
            }
            
            .nav-menu {
                flex-direction: column;
                padding: var(--spacing-md);
            }
            
            .nav-item {
                margin: var(--spacing-sm) 0;
            }
            
            .dropdown-menu {
                position: static;
                opacity: 1;
                visibility: visible;
                transform: none;
                box-shadow: none;
                border: none;
                background-color: var(--bg-tertiary);
                margin-top: var(--spacing-sm);
            }
        }
    `;
    document.head.appendChild(style);
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Export functions for use in other scripts
window.SilksongWiki = {
    initializeNavigation,
    initializeSearch,
    initializeMobileMenu,
    initializeScrollEffects,
    initializeAnimations,
    debounce,
    throttle
};
